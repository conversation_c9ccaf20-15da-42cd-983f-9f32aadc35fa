<div align="center">
  <h1>Lafayette Summer Jazz Workshop theory exam</h1>
  <p>✨Welcome to the repo for the web application for the Lafayette Summer Jazz Workshop✨</p>
</div>

<br />

### Tech Stack

![Next.js](https://img.shields.io/badge/Next.js-000?logo=nextdotjs&logoColor=fff&style=for-the-badge)
![React](https://img.shields.io/badge/react-%2320232a.svg?style=for-the-badge&logo=react&logoColor=%2361DAFB)
![TypeScript](https://img.shields.io/static/v1?style=for-the-badge&message=TypeScript&color=3178C6&logo=TypeScript&logoColor=FFFFFF&label=)
![MUI](https://img.shields.io/badge/MUI-%230081CB.svg?style=for-the-badge&logo=mui&logoColor=white)
![Firebase](https://img.shields.io/badge/Firebase-039BE5?style=for-the-badge&logo=Firebase&logoColor=white)
![HTML5](https://img.shields.io/badge/HTML5-E34F26?style=for-the-badge&logo=html5&logoColor=white)

#### Authored by:

- [Brett <PERSON>](https://github.com/BrettEastman)
- [Kasey Knudsen](https://github.com/kaseyknudsen)

## About

This web application was contracted to Lydian Labs Technology by Lafayette Summer Jazz Workshop director Kyle Athayde. Its functional purpose is to replace their current music theory entrance exam.
[Click here for more information about the camp](https://lafayettejazz.wpcomstaging.com/auditions/)
  
### Features

- Interactive notation: note input/erase and sharp and flat input via mouse click
- Automated grading
- Timed test: automatically goes to final submit button page after alotted time is up
- Clear, minimalist design to allow for a comfortable, distraction-free user experience


### Developer Features

- Firebase authentication with student info stored on Firestore database
- Firebase Cloud Storage for saving student PDFs
- Automated email to camp director (using Nodemailer) with a breakdown of how well each student did on the test
- Error monitoring with Sentry

### Examples
<img width="1705" alt="Screenshot 2024-06-26 at 5 20 08 PM" src="https://github.com/Lydian-Labs/lafsmw-theory-test/assets/76603041/7561b846-83dd-4730-ace6-96e80eaf20b8">
<img width="1704" alt="Screenshot 2024-06-26 at 5 20 40 PM" src="https://github.com/Lydian-Labs/lafsmw-theory-test/assets/76603041/baf2a1fa-f554-450c-a2fc-b8de87b152fe">
<img width="1703" alt="Screenshot 2024-06-26 at 5 22 22 PM" src="https://github.com/Lydian-Labs/lafsmw-theory-test/assets/76603041/81da7ffe-bb78-4fbc-bb56-8dacf6478aca">
<img width="1703" alt="Screenshot 2024-06-26 at 5 23 59 PM" src="https://github.com/Lydian-Labs/lafsmw-theory-test/assets/76603041/46dc2cb7-46c8-4e84-a680-486279173bb0">
<img width="1705" alt="Screenshot 2024-06-26 at 5 24 51 PM" src="https://github.com/Lydian-Labs/lafsmw-theory-test/assets/76603041/a5db8928-e493-4fa4-9c77-1b1b0e960d59">
<img width="1703" alt="Screenshot 2024-06-26 at 5 25 37 PM" src="https://github.com/Lydian-Labs/lafsmw-theory-test/assets/76603041/6029ace0-bf6c-465b-9798-031eb695e76a">
<img width="1703" alt="Screenshot 2024-06-26 at 5 26 13 PM" src="https://github.com/Lydian-Labs/lafsmw-theory-test/assets/76603041/d2f87f39-184a-47a5-8be6-0897df417193">
<img width="1684" alt="Screenshot 2024-06-26 at 5 29 18 PM" src="https://github.com/Lydian-Labs/lafsmw-theory-test/assets/76603041/340546bc-3107-44ae-b01f-baff0191d870">

<br />

Copyright© Lydian Labs Music Education Technology, 2024
