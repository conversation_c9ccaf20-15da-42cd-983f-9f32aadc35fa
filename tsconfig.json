{
  "compilerOptions": {
    "target": "es5",
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "baseUrl": ".",
    "types": ["@testing-library/jest-dom"],
    "paths": {
      "@/*": ["./*"]
    },
    "plugins": [
      {
        "name": "next"
      }
    ]
  },
  "include": [
    "next-env.d.ts",
    "**/*.ts",
    "**/*.tsx",
    "types.d.ts",
    ".next/types/**/*.ts"
  ],
  "exclude": [
    "./cypress.config.ts",
    //other exclusions that may help https://github.com/cypress-io/cypress/issues/22059#issuecomment-1428298264
    "node_modules",
    "cypress",
    "**/*.cy.tsx"
  ]
}
