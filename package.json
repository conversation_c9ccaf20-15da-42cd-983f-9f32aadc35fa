{"name": "lafsmw-theory-test", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "build-tokens": "style-dictionary build", "test": "jest", "test:watch": "jest --watch", "emulators:stop": "lsof -t -i:5000 -i:5001 -i:5002 -i:8080 -i:9000 -i:9099 -i:9199 -i:9090 | xargs kill -9"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fontsource/poppins": "^5.2.6", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.0", "@mui/system": "^7.1.0", "@next/third-parties": "^15.3.2", "@sentry/nextjs": "^9.17.0", "@types/vexflow": "^1.2.42", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "next": "^15.3.2", "nodemailer": "^6.10.1", "react": "^19", "react-dom": "^19", "react-icons": "^5.5.0", "typeface-montserrat": "^1.1.13", "vexflow": "^4.2.5"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/jest": "^29.5.14", "@types/node": "^22", "@types/nodemailer": "^6.4.17", "@types/react": "^19.1.3", "@types/react-dom": "^19", "@types/testing-library__jest-dom": "^6.0.0", "cypress": "^14.3.3", "cypress-firebase": "^4.2.1", "eslint": "^9", "eslint-config-next": "15.3.2", "firebase": "^11.7.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "style-dictionary": "^4.4.0", "ts-jest": "^29.3.2", "typescript": "^5.8.3"}}