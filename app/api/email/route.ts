import { NextRequest, NextResponse } from "next/server";
import { sendEmail<PERSON><PERSON>mailer } from "@/backend/sendEmailNodemailer";

export async function POST(request: NextRequest) {
  try {
    const { email, subject, text } = await request.json();

    await sendEmail<PERSON>odemailer(email, subject, text);

    return NextResponse.json({ message: "Em<PERSON> sent successfully" });
  } catch (error) {
    console.error("Error sending email:", error);
    return NextResponse.json(
      { error: "Error sending email", details: (error as Error).message },
      { status: 500 }
    );
  }
}
