{"global": {"md": {"sys": {"color": {"main-background-color": {"value": "#6B8287", "type": "color", "description": "This is the main background color that will appear behind the card"}, "card-background-color": {"value": "#F6F6F6", "type": "color", "description": "This will be the background color for the cards"}, "pressed-button-color": {"value": "#A6A7A7", "type": "color", "description": "This will be the color for the buttons when they are pressed"}, "enabled-button-color": {"value": "#F6D168", "type": "color", "description": "This will be the color when the buttons are enabled"}, "unfilled-progress-bar-color": {"value": "#CACACA", "type": "color", "description": "This will be the color for section of the progress bar that has not been filled"}, "filled-progress-bar-color": {"value": "#FDE55B", "type": "color", "description": "This will be the color for the section of the progress bar that has been filled"}}, "typescale": {"section-title": {"value": {"fontFamily": "<PERSON><PERSON><PERSON>", "fontWeight": "medium", "fontSize": "25", "lineHeight": "auto", "letterSpacing": ".25", "paragraphSpacing": "28"}, "type": "typography", "description": "Use this font for the \"Section 1: Key Signatures\" title (and for every section title)"}, "page-title": {"value": {"fontFamily": "<PERSON><PERSON><PERSON>", "fontWeight": "medium", "lineHeight": "20", "letterSpacing": ".5", "paragraphSpacing": "28", "fontSize": "20"}, "type": "typography", "description": "Use this for the page title on the right side above the staff: \"Write the following key signature\""}, "progress-bar-text": {"value": {"fontFamily": "<PERSON><PERSON><PERSON>", "fontWeight": "semi-bold", "fontSize": "14", "lineHeight": "auto", "letterSpacing": "0%", "paragraphSpacing": "0"}, "type": "typography", "description": "Use this font for the text above the progress bar: \"Question 1/45\""}, "user-instructions": {"value": {"fontFamily": "<PERSON><PERSON><PERSON>", "fontWeight": "regular", "fontSize": "11", "lineHeight": "auto", "letterSpacing": ".5", "paragraphSpacing": "28"}, "type": "typography", "description": "Use this text for the user instructions on the left side (after the underlined title)"}, "user-instruction-titles": {"value": {"fontFamily": "<PERSON><PERSON><PERSON>", "fontWeight": "semi-bold", "fontSize": "11", "lineHeight": "auto", "letterSpacing": ".5", "paragraphSpacing": "28", "textDecoration": "underlined"}, "type": "typography", "description": "use this text for the underlined titles and numbers in the user instructions: 1. Select the accidental:"}, "button-text": {"value": {"fontFamily": "<PERSON><PERSON><PERSON>", "fontWeight": "medium", "fontSize": "16", "lineHeight": "20", "paragraphSpacing": "20", "letterSpacing": ".1"}, "type": "typography", "description": "use this text for disabled buttons"}, "tutorial-title": {"value": {"fontFamily": "<PERSON><PERSON><PERSON>", "fontWeight": "medium", "fontSize": "18", "lineHeight": "auto", "letterSpacing": ".25", "paragraphSpacing": "28"}, "type": "typography", "description": "Use this for the \"Tutorial\" font"}}, "grid": {"medium-desktop-grid": {"value": "desktop grid", "type": "other", "description": "column count is 12 | row count is 5 | column gutter is 20 | row gutter is 0 | margin is 8"}}, "shadow": {"grey-background-drop-shadow-1": {"value": {"x": "0", "y": "783", "blur": "219", "spread": "0", "color": "#000000", "type": "dropShadow"}, "type": "boxShadow", "description": "drop shadow 1 for grey background"}, "grey-background-drop-shadow-2": {"value": {"x": "0", "y": "501", "blur": "200", "spread": "0", "color": "#000000 opacity 1%", "type": "dropShadow"}, "type": "boxShadow", "description": "drop shadow 2 for grey background"}, "grey-background-drop-shadow-3": {"value": {"x": "0", "y": "282", "blur": "169", "spread": "0", "color": "#000000 opacity 5%", "type": "dropShadow"}, "type": "boxShadow", "description": "box shadow 3 for grey background"}, "grey-background-drop-shadow-4": {"value": {"x": "0", "y": "125", "blur": "125", "spread": "0", "color": "#000000 opacity 9%", "type": "dropShadow"}, "type": "boxShadow", "description": "drop shadow #4 for grey background"}, "grey-background-drop-shadow-5": {"value": {"x": "0", "y": "31", "blur": "69", "spread": "0", "color": "#000000 opacity 10%", "type": "dropShadow"}, "type": "boxShadow", "description": "Drop shadow #5 for grey background"}}, "border-radius": {"value": "12", "type": "borderRadius", "description": "use the border radius of 12 for everything"}, "sizing": {"button-sizing": {"value": "40", "type": "sizing", "description": "buttons below the staff have a height of 40 | horizontal padding of 24 "}, "main-card-size": {"value": "Width 569 | Height 540", "type": "sizing", "description": "this is for the card on the right"}, "tutorial-card-size": {"value": "Width 273 | Height 456", "type": "sizing", "description": "Use this sizing for the tutorial card"}, "grey-background-size": {"value": "Width 1139 | Height 637", "type": "sizing", "description": "use this sizing for the grey rectangle behind the cards"}, "next-question-button-sizing": {"value": "40", "type": "sizing", "description": "Height 40 | Left side padding 24 | right side padding 16"}, "progress-bar-size": {"value": "Height 13 | <PERSON><PERSON><PERSON> 216", "type": "sizing", "description": "total progress bar size"}}, "spacing": {"grey-backround-box-margins": {"value": "margins", "type": "spacing", "description": "vertical margins 40 | horizontal margins 70"}}}}}, "$themes": [], "$metadata": {"tokenSetOrder": ["global"]}}