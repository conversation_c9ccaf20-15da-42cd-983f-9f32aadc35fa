export const keySigNotationInstructions = [
  {
    instructionTitle: "Select the accidental:",
    instructionText: `Choose the "Add Flat" or "Add Sharp" button to select the type of accidental you need to add.`,
  },
  {
    instructionTitle: "Place it on the staff:",
    instructionText: `After selecting the accidental, click on the exact line or space on the staff where the accidental belongs.`,
  },
  {
    instructionTitle: "Erase an accidental:",
    instructionText: `If you need to erase an accidental, first select the "Erase Accidental" button, then click on the note or accidental you wish to erase.`,
  },
  {
    instructionTitle: "Start over or continue:",
    instructionText: `You can erase the measure and start over by selecting the “Clear All” button. When finished, click "Continue".`,
  },
];

export const scalesNotationInstructions = [
  {
    instructionTitle: "Place notes:",
    instructionText: `Choose either "Enter Note" or "Erase Note" to place notes on the staff. Click on the exact line or space where you want the note to be placed or erased.`,
  },
  {
    instructionTitle: "Accidentals:",
    instructionText: `If scale has accidentals, after placing notes, click on the "Add Sharp" or "Add Flat" button, then click on the note where you want to add the accidental. Click a 2nd time for either a double-sharp or a double-flat. To erase, select the “Erase Accidental” button, then click on the note whose accidental you wish to erase.`,
  },
  {
    instructionTitle: "Start over or continue:",
    instructionText: `You can erase the measure and start over by selecting the “Clear All” button. When finished, click "Continue".`,
  },
];

export const chordsNotationInstructions = [
  {
    instructionTitle: "Place notes:",
    instructionText: `Choose either "Enter Note" or "Erase Note" to place notes on the staff. Click on the exact line or space where you want the note to be placed or erased.`,
  },
  {
    instructionTitle: "Accidentals:",
    instructionText: `If the chord has accidentals, after placing notes, click on the "Add Sharp" or "Add Flat" button, then click on the note where you want to add the accidental. Click a 2nd time for either a double-sharp or a double-flat. To erase, select the “Erase Accidental” button, then click on the note whose accidental you wish to erase.`,
  },
  {
    instructionTitle: "Start over or continue:",
    instructionText: `You can erase the measure and start over by selecting the “Clear All" button. When finished, click "Continue".`,
  },
];

export const keySigInputInstructions = [
  {
    instructionTitle: "Write the letter name of the following key signatures:",
    instructionText: `The first two are major key signatures, and the last two are minor key signatures. If the keyname has an accidental, use the following format: first, write the letter name of the key (not case sensitive), then the accidental. Use # for sharp, b for flat. For example, Db Major would be could be written as either "Db" or "db".`,
  },
  {
    instructionTitle: "Submit:",
    instructionText: `When finished, click the "Continue" button to move on to the next question.`,
  },
  {
    instructionTitle: "Allowed to return to this page:",
    instructionText: `If you need to double check your answers, you can return to this page later by clicking the back button ("<").`,
  },
];

export const chordTextInstructions = [
  {
    instructionTitle: "Write the name of the following chords:",
    instructionText:
      "Use the following format: root note, quality, and type of seventh chord.",
  },
  {
    instructionTitle: "Accidentals:",
    instructionText: `Use the following format: # for sharp, b for flat, x for double-sharp, and bb for double flat.`,
  },
  {
    instructionTitle: "Methods of writing chord name:",
    instructionText: `For example, the chord Cdim7 can also be written as C°7 or Co7. The chord Cmaj7 can also be written as CM7 or C∆7. The chord Caug7 can also be written as C+7.`,
  },
  {
    instructionTitle: "Key commands for symbols:",
    instructionText: `∆ = option-j,° = shift-option-8, ø = option-o.`,
  },
];
