:root {
  --primary10: #0d0e0b;
  --primary20: #2b2b2b;
  --primary30: #696969;
  --primary40: #848484;
  --primary50: #a0a0a0;
  --primary60: #b7b8b6;
  --primary70: #c9cac8;
  --primary80: #eeeeee;
  --primary90: #fcfcfc;
  --primary100: #ffffff;
  --primary110: #f8f8ff;
  --highLightBlue: rgba(
    173,
    216,
    230,
    0.4
  ); /* Light blue with some transparency for hover effect */
  --colorMainBackgroundColor: #eeeeee; /* This is the main background color that will appear behind the card */
  --colorCardBackgroundColor: #f6f6f6; /* This will be the background color for the cards */
  --colorPressedButtonColor: #a6a7a7; /* This will be the color for the buttons when they are pressed */
  --colorEnabledButtonColor: #f6d168; /* This will be the color when the buttons are enabled */
  --colorUnfilledProgressBarColor: #cacaca; /* This will be the color for section of the progress bar that has not been filled */
  --colorFilledProgressBarColor: #fde55b; /* This will be the color for the section of the progress bar that has been filled */
  --salmonWarningColor: #ff7f7f; /* This will be the color for the warning */
  --shadowGreyBackgroundDropShadow1: 0 783px 219px 0 #000000; /* drop shadow 1 for grey background */
  --shadowGreyBackgroundDropShadow2: 0 501px 200px 0 #000000 opacity 1%; /* drop shadow 2 for grey background */
  --shadowGreyBackgroundDropShadow3: 0 282px 169px 0 #000000 opacity 5%; /* box shadow 3 for grey background */
  --shadowGreyBackgroundDropShadow4: 0 125px 125px 0 #000000 opacity 9%; /* drop shadow #4 for grey background */
  --shadowGreyBackgroundDropShadow5: 0 31px 69px 0 #000000 opacity 10%; /* Drop shadow #5 for grey background */
  --cardShadow:
    0px 13px 28px 0px rgba(0, 0, 0, 0.1), 0px 50px 50px 0px rgba(0, 0, 0, 0.09),
    0px 113px 68px 0px rgba(0, 0, 0, 0.05),
    0px 201px 80px 0px rgba(0, 0, 0, 0.01), 0px 314px 88px 0px rgba(0, 0, 0, 0);
  --borderRadius: 20px; /* use the border radius of 12 for everything */
  --sizingButtonSizing: 40px; /* buttons below the staff have a height of 40 | horizontal padding of 24  */
}

* {
  margin: 0;
  padding: 0;
}

body {
  background-color: var(--colorMainBackgroundColor);
}
